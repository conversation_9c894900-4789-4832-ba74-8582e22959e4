{"appTitle": "Fitness Calorie Tracker", "loading": "Loading...", "nav": {"dashboard": "Dashboard", "log": "Log", "ai_assistant": "AI Assistant", "settings": "Settings"}, "dashboard": {"title": "Dashboard", "intake": "Intake", "burned": "Burned", "net": "Net", "goal_progress": "Daily Goal Progress", "consumed": "Consumed", "remaining": "Remaining", "of": "/", "kcal": "kcal", "net_calories_7_days": "Net Calories (Last 7 Days)", "net_calories": "Net Calories"}, "log": {"food": "Food", "exercise": "Exercise", "log_food": "Log Food", "food_name": "Food Name", "calories": "Calories", "meal": {"breakfast": "Breakfast", "lunch": "Lunch", "dinner": "Dinner", "snack": "Snack"}, "add_food": "Add Food", "scan_food": "<PERSON><PERSON>", "upload_image": "Upload", "log_exercise": "Log Exercise", "exercise_name": "Exercise Name", "duration_mins": "Duration (minutes)", "duration_unit": "min", "calories_burned": "Calories Burned", "add_exercise": "Add Exercise", "todays_log": "Today's Log", "no_food": "No food logged yet.", "no_exercise": "No exercise logged yet.", "date_log": "'s Log"}, "ai": {"title": "AI Assistant", "not_configured": "AI Assistant Not Configured", "not_configured_desc": "Please enter your Google Gemini API Key in the Settings page to enable this feature.", "go_to_settings": "Go to Settings", "welcome": "Hello! How can I help you?", "example_prompt": "e.g., \"Give me some low-calorie lunch ideas.\"", "ask_placeholder": "Ask AI for advice..."}, "settings": {"title": "Settings", "ai_settings": "AI Settings", "ai_settings_desc": "Configure your AI assistant. The API key is stored securely on your device.", "api_key": "Google Gemini API Key", "api_key_placeholder": "Paste your API key here", "api_key_required_error": "API Key is not set. Please add it in Settings.", "ai_model": "AI Model", "model": {"flash": "Gemini 2.5 Flash (Recommended)", "pro": "Gemini 1.5 Pro", "gemini-pro": "Gemini Pro"}, "language": {"label": "Language", "en": "English", "zh-TW": "繁體中文 (Traditional Chinese)"}, "save": "Save All Settings", "saved": "Settings saved!", "user_profile": {"title": "User Profile", "desc": "Provide your details to calculate a more accurate daily calorie goal (TDEE).", "age": "Age", "sex": "Sex", "select_sex": "Select Sex", "male": "Male", "female": "Female", "weight": "Weight (kg)", "height": "Height (cm)", "activity_level": "Activity Level", "activity": {"sedentary": "Sedentary (little or no exercise)", "light": "Lightly active (light exercise/sports 1-3 days/week)", "moderate": "Moderately active (moderate exercise/sports 3-5 days/week)", "active": "Active (hard exercise/sports 6-7 days a week)", "very_active": "Very active (very hard exercise & physical job)"}, "calculate_goal": "Calculate & Set Goal", "validation_error": "Please fill in all profile fields to calculate your goal.", "tdee_result_1": "Your estimated daily calorie need is", "tdee_result_2": "Your daily goal has been updated."}, "data_management": {"title": "Data Management", "desc": "Export your data for backup, or import it on a new device.", "export": "Export Data", "import": "Import Data", "import_confirm": "Are you sure you want to import data? This will overwrite all current data.", "import_success": "Data imported successfully! The app will now reload.", "import_error": "Failed to import data. The file may be corrupt or in the wrong format."}}, "camera": {"modal_title": "Scan Food with AI", "scanning": "Scanning...", "scan_error": "Could not analyze image. Please try again.", "unidentified": "Food not identified. Please enter manually.", "permission_error": "Camera permission is required. Please enable it in your browser settings."}, "general": {"today": "Today"}}